import moment from "moment";
import { useContext, useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Youtube from "../../assets/images/svg/youtube";
import Toast from "../../components/ui/Toast";
import { apiBaseURL, CALCULATION_ON_TYPE, SHIPPING_ADDRESS_TYPE_LIST, TABLE_HEADER_TYPE, toastType, TRANSACTION_TYPE } from "../../constants";
import { StateContext } from "../../context/StateContext";
import {
    calculateAdditionalCharges,
    calculateAdditionalClassification,
    calculateAddLessCharges,
    calculateClassification,
    calculateTotals,
    customToFixed,
    formattedDate,
    gstCalculate,
    RoundOffMethod,
    RoundOffMethodForTds,
    setGSTCalculationType,
} from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import CustomHelmet from "../../shared/helmet";
import Loader from "../../shared/loader";
import {
    areAllProductsExemptOrNA,
    calculateGSTFlags,
    checkAllAdditionalChargesNaAndExempt,
    convertToFormData,
    fetchPurchaseOrderNumber,
    findGstRate,
    prepareItemsData,
    prepareLedgerData,
    preparePurchaseData,
    prepareSaveAndNewData,
    unitOptionWithKey
} from "../../shared/prepareData";
import { correctClassificationNatureType, getEditLedgerFromList } from "../../shared/sharedFunction";
import { useTransactionShortcuts, useModalFocusTracking } from "../../shared/shortcut-keys";
import { errorToast } from "../../store/actions/toastAction";
import { getAdvancePayment } from "../../store/advance-payment/advancePaymentSlice";
import { fetchBrokerList } from "../../store/broker/brokerSlice";
import { fetchClassificationList } from "../../store/classification/classificationSlice";
import { fetchCompanyDetails, fetchUserPermission } from "../../store/company/companySlice";
import { fetchConfigurationList, rearrangeItemList, resetConfiguration } from "../../store/configuration/configurationSlice";
import { fetchDispatchAddressList } from "../../store/dispatchAddress/dispatchAddressSlice";
import { fetchPurchaseInvoiceNumber } from "../../store/invoice/invoiceSlice";
import { fetchItemList } from "../../store/item/itemSlice";
import {
    fetchAdditionalLedgerList,
    fetchAddlessLedgerList,
    fetchItemLedgerDetail,
    fetchPartyDetail,
    fetchPartyList,
    fetchPaymentLedgerList,
    fetchPaymentModeList,
} from "../../store/ledger/ledgerSlice";
import { fetchPrevNextUrl } from "../../store/prev-next/prev-nextSlice";
import { addPurchase, deletePurchase, updatePurchase } from "../../store/purchase/purchaseSlice";
import { fetchTcsList, fetchTdsList } from "../../store/rate/rateSlice";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import { fetchTransportList } from "../../store/transport/transportSlice";
import Error404Page from "../common/404";
import SaleInvoiceDetail from "../common/InvoiceDetail";
import SaleItems from "../common/Items";
import WarningModal from "../common/WarningModal";
import ConfigurationModal from "../modal/Configuration/ConfigurationModal";
import DeleteWarningModal from "../modal/DeleteWarningModal";

const PurchasesTransaction = ({ id, singleSale, isDuplicate, purchaseOrderId }) => {
    const purchaseEdit = window.location.pathname.includes("edit");
    const isInWord = true;
    const url = window.location.origin;
    const bookPurchase = window.location.pathname.includes("purchase-create");
    const shippingAddressType = bookPurchase ? SHIPPING_ADDRESS_TYPE_LIST.PURCHASE_ORDER : SHIPPING_ADDRESS_TYPE_LIST.PURCHASE


    const { roundOffOption, gstOptions, classificationOptions, tableHeaderList, accountingTableHeader } = useDropdownOption();

    const {
        defaultItem,
        defaultAccountingItem,
        items,
        setItems,
        accountingItems,
        setAccountingItems,
        gstValue,
        setGstValue,
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        setPartyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        gstCalculation,
        setGstCalculation,
        paymentLedgerDetail,
        setPaymentLedgerDetail,
        classification,
        setClassification,
        additionalGst,
        setAdditionalGst,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        setLocalDispatchAddress,
        gstQuote,
        setGstQuote,
        additionalCharges,
        setAdditionalCharges,
        tcsRate,
        setTcsRate,
        addLessChanges,
        setAddLessChanges,
        cessValue,
        selectedAddress,
        sameAsBill,
        setSameAsBill,
        itemType,
        setItemType,
        setChangeTax,
        grandTotal,
        setGrandTotal,
        mainGrandTotal,
        setMainGrandTotal,
        setCessValue,
        isEditCalculation,
        setIsEditCalculation,
        finalAmount,
        setFinalAmount,
        setConfigurationURL,
        setConfigurationTableList,
        setConfigurationFooterList,
        setConfigurationModalName,
        purchaseInvoice,
        setPurchaseInvoice,
        isIGSTCalculation,
        setIsIGSTCalculation,
        isSGSTCalculation,
        setIsSGSTCalculation,
        loader,
        setLoader,
        taxableValue,
        setTaxableValue,
        isTcsAmountChange,
        isChangedTcs,
        classificationType,
        changeTax,
        setUpdatePurchaseNumberOptions,
        setInvoiceValue,
        deleteTransaction,
        openDeleteTransactionModel,
        closeDeleteTransactionModel,
        isCheckGstType,
        selectedAdvancePayment,
        setSelectedAdvancePayment,
        setShowPaymentTable,
        isDisable,
        setIsDisable,
        showDeleteWarningModel,
        setShowDeleteWarningModel,
        customFieldListTransaction,
        setCustomHeaderListTransaction,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        setIsChangePartyId
    } = useContext(StateContext);

    const [isShowGstValue, setIsShowGstValue] = useState(false);
    const [tcsValue, setTCSValue] = useState(0);
    const [tcsRateValue, setTCSRateValue] = useState(0);
    const [shippingValue, setShippingValue] = useState(0);

    const [packingCharge, setPackingCharge] = useState(0);
    const [updatedTableHeader, setUpdatedTableHeader] = useState([]);

    useEffect(() => {
        document.getElementById("showName").innerHTML = id ? "Edit Purchase" : "Add Purchase";
        // if (!purchaseEdit) {
        //     setIsEditCalculation(true);
        // }
        setConfigurationModalName("Purchase Configuration");
    }, []);

    const dispatch = useDispatch();
    const purchaseReturnRef = useRef(null);
    const formRef = useRef(null);
    const {
        broker,
        company,
        invoice,
        item,
        ledger,
        table,
        dispatchAddress,
        configuration,
        sale,
        purchaseOrder,
        prevNext,
        purchase,
    } = useSelector(selector => selector);

    const fetchPurchaseDetail = purchaseOrder?.purchaseOrderById;

    const configurationList = configuration?.configuration;

    useEffect(() => {
        setConfigurationTableList(
            [
                company?.company?.is_gst_applicable && {
                    name: "Change GST Details",
                    key: "is_change_gst_details",
                    value: configurationList?.header?.is_change_gst_details,
                    position: 1,
                },
                {
                    name: "Additional Ledger Description",
                    key: "is_additional_ledger_description",
                    value: configurationList?.item_table_configuration
                        ?.is_additional_ledger_description,
                    position: 3,
                },
                {
                    name: "Discount 2",
                    key: "is_enabled_discount_2",
                    value: configurationList?.item_table_configuration?.is_enabled_discount_2,
                    position: 7,
                },
                {
                    name: "MRP",
                    key: "is_enabled_mrp",
                    value: configurationList?.item_table_configuration?.is_enabled_mrp,
                    position: 6,
                },
            ].filter(Boolean),
        );

        setConfigurationFooterList([
            {
                name: "TCS",
                key: "is_enabled_tcs_details",
                value: configurationList?.footer?.is_enabled_tcs_details,
                position: 1,
            },
            {
                name: "TDS",
                key: "is_enabled_tds_details",
                value: configurationList?.footer?.is_enabled_tds_details,
                position: 2,
            },
            {
                name: "Payment Details",
                key: "is_enabled_payment_details",
                value: configurationList?.footer?.is_enabled_payment_details,
                position: 6,
            },
        ]);
    }, [configurationList, company]);

    useEffect(() => {
        if (fetchPurchaseDetail?.length !== 0) {
            const purchaseOrderToPurchase = fetchPurchaseDetail?.purchaseOrderTransaction
            if (purchaseOrderId) {
                dispatch(fetchPartyDetail(purchaseOrderToPurchase?.party_ledger_id));
            }
            if (purchaseOrderToPurchase?.party_ledger_id) {
                dispatch(fetchPartyList({ ids: [purchaseOrderToPurchase?.party_ledger_id] }));
            } else {
                dispatch(fetchPartyList());
            }

            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 2;
            const tds_type = 1;
            const is_call_payment_ledger = true;
            if (fetchPurchaseDetail?.allTransactionItems) {
                fetchPurchaseDetail?.allTransactionItems?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: purchaseOrderToPurchase});
            fetchPurchaseOrderNumber({
                fetchPurchaseDetail,
                purchaseOrderToPurchase,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    gstQuote,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setTaxableValue,
                    setCessValue,
                    setUpdatePurchaseNumberOptions,
                    localDispatchAddress,
                    setLocalDispatchAddress,
                    setCustomHeaderListTransaction,
                    setSameAsBill
                },
                dispatch,
                tableHeaderList,
                accountingTableHeader,
                addLessChanges,
                additionalCharges,
                classificationOptions,
                purchaseOrderId,
                bookPurchase,
                company
            });
        }
    }, [fetchPurchaseDetail, classificationOptions, company?.company]);

    useEffect(() => {
        if (
            purchase?.getPurchaseDetailByOrderNumber &&
            purchase?.getPurchaseDetailByOrderNumber?.length !== 0
        ) {
            const itemsList = [...purchase?.getPurchaseDetailByOrderNumber?.allTransactionItems];
            const invoiceDetail =
                purchase?.getPurchaseDetailByOrderNumber?.purchaseOrderTransaction;

            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 2;
            const tds_type = 1;
            const is_call_payment_ledger = true;
            if (itemsList) {
                itemsList?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: invoiceDetail});

            if (
                purchase?.getPurchaseDetailByOrderNumber?.purchaseOrderTransaction?.order_type == 1
            ) {
                dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, 2));
                setItemType("accounting");
            } else {
                setItemType("item");
                dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, 1));
            }
            setSameAsBill(invoiceDetail?.same_as_billing);
            setBrokerDetail({
                ...brokerDetail,
                broker_id: invoiceDetail?.broker_id,
                broker_percentage: invoiceDetail?.brokerage,
                brokerage_on_value: invoiceDetail?.brokerage_on_value_type,
            });
            setTransporterDetail({
                ...transporterDetail,
                transport_id: invoiceDetail?.transport_id,
                transporter_document_date: invoiceDetail?.transporter_document_date
                    ? formattedDate(invoiceDetail?.transporter_document_date)
                    : null,
                transporter_document_number: invoiceDetail?.transporter_document_number,
                transporter_vehicle_number: invoiceDetail?.transporter_vehicle_number,
            });
            setCustomHeaderListTransaction(invoiceDetail?.custom_values)

            setOtherDetail({
                ...otherDetail,
                po_number: invoiceDetail?.po_no,
                date: invoiceDetail?.po_date ? formattedDate(invoiceDetail?.po_date) : null,
                creditPeriod: invoiceDetail?.credit_period,
                creditPeriodType: invoiceDetail?.credit_period_type ?? 1,
            });

            const updatedItemsList =
                itemsList &&
                itemsList.map(item => {
                    if (invoiceDetail.order_type == 2) {
                        setChangeTax(item?.with_tax);
                        const unitOptionDetail = unitOptionWithKey(item.unitOfArray);

                        // Return the updated item
                        return {
                            ...item,
                            transaction_item_id: item?.id,
                            selectedItem: item?.item_id,
                            total: item?.total,
                            updatedTotal: item?.total,
                            sgstValue: item?.classification_cgst_tax,
                            cgstValue: item?.classification_cgst_tax,
                            cessValue: customToFixed(item?.classification_cess_tax, 2),
                            multiQuantity: [1, 0, 0, 0],
                            free_quantity: item?.free_quantity,
                            selectedUnit: item?.unit_id,
                            selectedLedger: item?.ledger_id,
                            gst_id: item?.gst_id,
                            hsn_code: item?.hsn_code,
                            gst: item?.gst_tax_percentage,
                            rateWithGst: item?.rpu_with_gst,
                            rateWithoutGst: item?.rpu_without_gst,
                            updatedRateWithGst: item?.rpu_with_gst,
                            // updatedRateWithoutGst: item?.rpu_without_gst,
                            // sub total issue generate when p o number with tax and use in purchase
                            updatedRateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            quantity: item?.quantity,
                            mrp: item?.mrp || 0,
                            cessRate: item?.cess_rate,
                            gst_copy: item?.gst_tax_percentage,
                            with_tax: changeTax ? 1 : 0,
                            rpu: changeTax ? item?.rateWithGst : item?.rateWithoutGst,
                            additional_description: item?.description || item?.additional_description || "",
                            itemUnitOption: unitOptionDetail,
                            conversationRate: item?.conversion_rate,
                            item_decimal_places: item?.item_decimal_places,
                            item_decimal_places_for_rate: item?.item_decimal_places_for_rate,
                            secondaryUnitOfMeasurement: item?.secondary_unit_of_measurement,
                            decimal_places: item?.decimal_places ?? 2,
                            decimal_places_for_rate: item?.decimal_places_for_rate ?? 2,
                            discountType: item?.discount_type,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2 || 1,
                            discountValue_2: item?.discount_value_2,
                            isShowDelete: true,
                        };
                    } else {
                        setChangeTax(item?.with_tax);
                        return {
                            id: item?.id,
                            transaction_item_id: item?.id,
                            selectedLedger: item?.ledger_id,
                            additional_description: item?.additional_description,
                            rateWithGst: item?.rpu_with_gst,
                            gst: item?.gst_tax_percentage,
                            gst_id: item?.gst_id,
                            discountType: item?.discount_type || 1,
                            discountValue: item?.discount_value,
                            discountType_2: item?.discount_type_2 || 1,
                            discountValue_2: item?.discount_value_2,
                            total: item?.total,
                            cgstValue: item?.classification_cgst_tax,
                            igstValue: item?.classification_igst_tax,
                            sgstValue: item?.classification_sgst_tax,
                            cessValue: (item?.cess_rate * item?.total) / 100,
                            cessRate: item?.cess_rate,
                            with_tax: item?.with_tax ? 1 : 0,
                            rpu: item?.rpu_with_gst || 0,
                            updatedRateWithGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            rateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            updatedRateWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            amountWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                            updatedAmountWithoutGst: item?.with_tax
                                ? item?.rpu_with_gst
                                : item?.rpu_without_gst?.toFixed(2),
                        };
                    }
                });

            const nature_type =
                itemsList?.length > 0 && itemsList[0]?.classification_nature_type?.id;

            const nature_name = classificationOptions.find(item => item.value == nature_type);

            const updatedAdditionalCharges = !invoiceDetail?.additional_charges ||
                invoiceDetail?.additional_charges?.length == 0
                ? [
                    {
                        ac_ledger_id: null,
                        ac_type: 1,
                        ac_value: 0,
                        ac_gst_rate_id: {
                            label: "",
                            value: 0,
                            rate: 0,
                        },
                        ac_total: 0,
                    },
                ]
                : invoiceDetail?.additional_charges?.map(charge => {
                    return {
                        ac_ledger_id: charge?.ledger_id,
                        ac_type: charge?.charge_type,
                        ac_value: charge?.value,
                        ac_gst_rate_id: {
                            value: charge?.gst_rate_id,
                            rate: Number(charge?.gst_percentage),
                            label: `${charge?.gst_percentage}%`,
                        },
                        ac_total: charge?.total_without_tax,
                    };
                });
            setIsEditCalculation(true);
            setAdditionalCharges({
                upload_document: invoiceDetail?.media?.map(media => {
                    return { original_url: media?.original_url, id: media?.id };
                }),
                note: invoiceDetail?.narration,
                terms_and_conditions: invoiceDetail?.term_and_condition,
                additional_detail: updatedAdditionalCharges

            });
            const updatedAddLessCharges = invoiceDetail?.add_less?.length == 0
                ? [
                    {
                        al_ledger_id: null,
                        al_is_show_in_print: 1,
                        al_type: 1,
                        al_value: null,
                        al_total: 0,
                    },
                ]
                : invoiceDetail?.add_less?.map(item => {
                    return {
                        al_ledger_id: item.ledger_id,
                        al_is_show_in_print: item.is_show_in_print,
                        al_type: item.type,
                        al_value:
                            item.type == 2 ? parseFloat(item.value) : parseFloat(item.total),
                        al_total: parseFloat(item.total),
                    };
                });
            setClassification({
                ...classification,
                classification_nature_name: nature_name?.label,
                classification_nature: nature_type,
                rcm_applicable: itemsList[0]?.classification_is_rcm_applicable ? true : false,
            });

            if(configurationList?.header?.is_change_gst_details){
                setGSTCalculationType(
                    {
                        classification_nature_name: nature_name?.label,
                    },
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                );
            }
            if (updatedItemsList?.length > 0) {
                if (invoiceDetail.order_type == 2) {
                    setItems(updatedItemsList);
                    setAccountingItems(defaultAccountingItem);
                } else {
                    setItems(defaultItem);
                    setAccountingItems(updatedItemsList);
                }
            }


            const { itemTotal, gstTotal, totalIgst, cessTotal } = calculateTotals(
                updatedItemsList,
                classificationType
            );

            setGrandTotal(itemTotal);

            const addition_charges = calculateAdditionalCharges(
                { additional_detail: updatedAdditionalCharges },
                itemTotal,
                isIGSTCalculation
            );
            setGstValue({
                ...gstValue,
                sgstValue: invoiceDetail?.sgst,
                cgstValue: invoiceDetail?.cgst,
                igstValue: invoiceDetail?.igst,
            });

            const gstTotalWithAmount = isIGSTCalculation
                ? parseFloat(gstValue?.igstValue || 0)
                : parseFloat(gstValue?.sgstValue || 0) + parseFloat(gstValue?.cgstValue);

            const total =
                parseFloat(company?.company?.is_gst_applicable ? gstTotalWithAmount : 0) +
                parseFloat(addition_charges?.addition_charges + itemTotal) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue : 0) +
                parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate?.tcs_amount || 0 : 0);

            setTaxableValue(
                parseFloat(addition_charges?.addition_charges) + parseFloat(itemTotal)
            );
            const addLessCharges = calculateAddLessCharges(updatedAddLessCharges, total);
            setAddLessChanges(addLessCharges);

            setAdditionalGst(addition_charges?.acTotal);
            setCessValue(parseFloat(cessTotal));

            let additionalGstTotal = parseFloat(addition_charges?.acTotal);
            if (isSGSTCalculation) {
                additionalGstTotal = customToFixed(
                    parseFloat(addition_charges?.acTotal) * 2,
                    2
                );
            }
            const totalAddLessAmount = addLessCharges?.reduce((sum, change) => {
                if (change.al_type === 1) {
                    return sum + (parseFloat(change.al_value) || 0);
                } else if (change.al_type === 2) {
                    return sum + (parseFloat(change.al_total) || 0);
                }
                return sum;
            }, 0);
            const roundOffAmount = customToFixed(invoiceDetail?.round_off_amount, 2);
            setGstCalculation({
                ...gstCalculation,
                rounding_amount: roundOffAmount,
                round_of_amount: roundOffAmount
            })
            setMainGrandTotal(invoiceDetail?.grand_total - roundOffAmount);
            setFinalAmount(invoiceDetail?.grand_total);
        }
    }, [setItems, purchase?.getPurchaseDetailByOrderNumber, classificationOptions]);

    useEffect(() => {
            const newPurchaseData = JSON.parse(localStorage.getItem('saveAndNewData'));

            if (newPurchaseData) {
                setIsChangePartyId(true);
                dispatch(fetchPartyDetail(newPurchaseData?.supplier_ledger_id));
                dispatch(fetchShippingAddressList(parseFloat(newPurchaseData?.supplier_ledger_id), shippingAddressType, id));
                if (newPurchaseData?.supplier_ledger_id) {
                    dispatch(fetchPartyList({ ids: [newPurchaseData?.supplier_ledger_id] }));
                } else {
                    dispatch(fetchPartyList());
                }

                const newData = newPurchaseData;
                prepareSaveAndNewData(newData, setGstQuote, setPartyAddress, additionalCharges, setAdditionalCharges, setItemType);
            }

        }, []);

    useEffect(() => {
        if (singleSale) {
            dispatch(fetchPartyDetail(singleSale?.supplier_ledger_id));
            if (singleSale?.supplier_ledger_id) {
                dispatch(fetchPartyList({ ids: [singleSale?.supplier_ledger_id] }));
            } else {
                dispatch(fetchPartyList());
            }
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 2;
            const tds_type = 1;
            const is_call_payment_ledger = true;

            if (singleSale?.purchase_transaction_items) {
                singleSale?.purchase_transaction_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }else if (singleSale?.purchase_transaction_ledger) {
                singleSale?.purchase_transaction_ledger?.forEach(item => {
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: singleSale});
            if (singleSale?.advance_payment?.length > 0) {
                setShowPaymentTable(true);
                dispatch(getAdvancePayment(singleSale?.supplier_ledger_id, "purchase", id));
            }

            preparePurchaseData(singleSale, {
                id,
                setStateFunctions: {
                    setPurchaseInvoice,
                    invoiceDetail,
                    setInvoiceDetail,
                    gstQuote,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setTaxableValue,
                    setCessValue,
                    setUpdatePurchaseNumberOptions,
                    setSelectedAdvancePayment,
                    setCustomHeaderListTransaction,
                    setSameAsBill
                },
                dispatch,
                tableHeaderList,
                accountingTableHeader,
                addLessChanges,
                additionalCharges,
                classificationOptions,
                isDuplicate
            });
        }
    }, [singleSale, classificationOptions]);

    useEffect(() => {
        setLoader(true);
        dispatch(fetchConfigurationList(apiBaseURL.PURCHASE_CONFIGURATION));
        setConfigurationURL(apiBaseURL.PURCHASE_CONFIGURATION);
        dispatch(fetchPurchaseInvoiceNumber());
        dispatch(fetchCompanyDetails());
        if (!purchaseEdit) {
            dispatch(fetchPartyList());
        }
        dispatch(fetchUserPermission());
        dispatch(fetchDispatchAddressList());
        setTimeout(() => {
            dispatch(fetchClassificationList(2));
            dispatch(fetchBrokerList());
            dispatch(fetchTransportList());
            dispatch(fetchPrevNextUrl({ type: "2", id: id }));
            if (!purchaseEdit) {
                dispatch(fetchItemList());
                dispatch(fetchItemLedgerDetail());
                dispatch(fetchTcsList({id:2}));
                dispatch(fetchTdsList({id:1}));
                dispatch(fetchPaymentLedgerList());
                dispatch(fetchAddlessLedgerList());
                dispatch(fetchAdditionalLedgerList());
            }
            dispatch(fetchPaymentModeList(2));
        }, 1500);
    }, [dispatch]);

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            // !classification?.classification_nature_name &&
            localDispatchAddress &&
            company?.company?.is_gst_applicable && ((!id && !isDuplicate && !purchaseOrderId) || isCheckGstType)
        ) {
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [
        ledger?.partyDetail?.billingAddress?.state_id,
        localDispatchAddress,
        gstQuote.original_inv_no,
        isCheckGstType,
        partyAddress?.billingAddress
    ]);

    useEffect(() => {
        dispatch(resetConfiguration());
    }, []);

    useEffect(() => {
        setTimeout(() => {
            if(configurationList){
                setLoader(false);
            }
        },300)
    },[configurationList])

    useEffect(() => {
        if (!configurationList?.header?.is_change_gst_details && company?.company?.is_gst_applicable && isCheckGstType) {
            setClassification({
                rcm_applicable: false,
                classification_nature_name: '',
                classification_nature: 0
            })
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [configurationList?.header?.is_change_gst_details])

    useEffect(() => {
        setLocalDispatchAddress(dispatchAddress?.dispatchAddress);
    }, [dispatchAddress?.dispatchAddress]);

    useEffect(() => {
        if (isEditCalculation) {
            const { itemTotal, cessTotal } = calculateTotals(
                itemType === "accounting" ? accountingItems : items,
                classificationType
            );
            setGrandTotal(itemTotal);
            const addition_charges = calculateAdditionalCharges(
                additionalCharges,
                itemTotal,
                isIGSTCalculation,
            );
            setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
            // setCessValue(cessTotal);
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) + parseFloat(cessValue || 0) + parseFloat(gstValue?.igstValue || 0) + parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) + parseFloat(cessValue || 0) + parseFloat(gstValue?.sgstValue || 0) + parseFloat(additionalGst || 0) + parseFloat(additionalGst || 0) + parseFloat(gstValue?.cgstValue || 0);

            // Update the invoice value
            setInvoiceValue(newInvoiceValue);
            if (taxableValue > 0 && tcsRate?.tcs_rate && !isTcsAmountChange && isChangedTcs) {
                const tcsRateAmount =
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100))
                        : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100));
                setTcsRate({
                    ...tcsRate,
                    tcs_amount: parseFloat(tcsRateAmount || 0).toFixed(2),
                });
            }
            const cgst =
                !isIGSTCalculation && isSGSTCalculation && !classification.rcm_applicable
                    ? customToFixed(parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0), 2)
                    : 0;
            const sgst =
                !isIGSTCalculation && isSGSTCalculation && !classification.rcm_applicable
                    ? customToFixed(parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0), 2)
                    : 0;
            const igst =
                isIGSTCalculation && !classification.rcm_applicable
                    ? customToFixed(parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0), 2)
                    : 0;

            const cgstWithoutRCM =
                    !isIGSTCalculation && isSGSTCalculation
                        ? customToFixed(parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0), 2)
                        : 0;
            const sgstWithoutRCM =
                    !isIGSTCalculation && isSGSTCalculation
                        ? customToFixed(parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0), 2)
                        : 0;
            const igstWithoutRCM =
                    isIGSTCalculation
                        ? customToFixed(parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0), 2)
                        : 0;

            const total =
                parseFloat(itemTotal) +
                parseFloat(addition_charges?.addition_charges || 0) +
                parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate.tcs_tax_id ? isNaN(tcsRate.tcs_amount) ? 0 : tcsRate.tcs_amount : 0 || 0 : 0) +
                // (parseFloat(totalAddLessAmount) || 0) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? sgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? igst : 0);

            const totalWithoutAddLess =
                parseFloat(itemTotal) +
                parseFloat(addition_charges?.addition_charges || 0) +
                parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate.tcs_tax_id ? isNaN(tcsRate.tcs_amount) ? 0 : tcsRate.tcs_amount : 0 || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cgstWithoutRCM : 0) +
                parseFloat(company?.company?.is_gst_applicable ? sgstWithoutRCM : 0) +
                parseFloat(company?.company?.is_gst_applicable ? igstWithoutRCM : 0);

            const updatedAddLessCharges = calculateAddLessCharges(
                addLessChanges,
                totalWithoutAddLess
            );
            const totalAddLessAmount = updatedAddLessCharges?.reduce((sum, change) => {
                if (change.al_type === 1) {
                    return sum + (parseFloat(change.al_value) || 0);
                } else if (change.al_type === 2) {
                    return sum + (parseFloat(change.al_total) || 0);
                }
                return sum;
            }, 0);
            setMainGrandTotal(parseFloat(total + (totalAddLessAmount || 0)));
            const gstData = calculateAdditionalClassification(classification, addition_charges?.acTotal);
            setAdditionalGst(gstData);
        }
    }, [
        taxableValue,
        tcsRate.tcs_amount,
        grandTotal,
        mainGrandTotal,
        cessValue,
        items,
        addLessChanges,
        gstValue,
        additionalGst,
        additionalCharges,
        configurationList?.footer?.round_off_method,
        configurationList?.footer?.is_enabled_tcs_details
    ]);

    const calculateLocalTotal = () => {
        const { grandFinalAmount, roundOffAmount } = RoundOffMethod(
            mainGrandTotal,
            (purchaseOrderId || id) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method,
        );
        setGstCalculation({
            ...gstCalculation,
            is_gst_enabled: company?.company?.is_gst_applicable,
            round_of_amount: roundOffAmount,
        });
        setFinalAmount(grandFinalAmount);
    };

    useEffect(() => {
        if (isEditCalculation) {
            calculateLocalTotal();
        }
    }, [mainGrandTotal, configurationList?.footer?.round_off_method, company, additionalCharges, isCheckGstType]);

    useEffect(() => {
        if(isEditCalculation) {
        const newInvoiceValue = isIGSTCalculation
            ? parseFloat(taxableValue || 0) + parseFloat(cessValue || 0) + parseFloat(gstValue?.igstValue || 0) + parseFloat(additionalGst || 0)
            : parseFloat(taxableValue || 0) + parseFloat(cessValue || 0) + parseFloat(gstValue?.sgstValue || 0) + parseFloat(additionalGst || 0) + parseFloat(additionalGst || 0) + parseFloat(gstValue?.cgstValue || 0);

        // Update the invoice value
        setInvoiceValue(newInvoiceValue);
        if (tcsRate?.tcs_rate) {
            setTcsRate(prevRate => ({
                ...prevRate,
                tcs_amount:
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(2)
                        : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(2),
            }));
        }
        if (paymentLedgerDetail.tds_rate) {
            setPaymentLedgerDetail({
                ...paymentLedgerDetail,
                tds_amount: RoundOffMethodForTds(taxableValue * (paymentLedgerDetail?.tds_rate / 100), paymentLedgerDetail?.rounding_type),
            });
        }
    }
    }, [taxableValue]);

    useEffect(() => {
        let updatedHeaders = table?.tableHeader || [];
        if (
            configurationList?.item_table_configuration?.is_enabled_discount_2 === false ||
            configurationList?.item_table_configuration?.is_enabled_discount_2 == 0
        ) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.DISCOUNT_2);
        }
        if (configurationList?.item_table_configuration?.is_enabled_mrp === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.MRP);
        }
        if (configurationList?.item_table_configuration?.is_enable_free_quantity === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.FREE_QUANTITY);
        }
        if (!configurationList?.item_table_configuration?.is_enabled_hsn_code) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.HSN_SAC);
        }
        const filteredColumns = updatedHeaders.filter(col => col?.is_enabled !== false);
        setUpdatedTableHeader(filteredColumns);
    }, [table?.tableHeader, configurationList]);

    useEffect(() => {
        if (!id) {
            const maxDate = moment(company?.company?.currentFinancialYear?.yearEndDate).format(
                "YYYY-MM-DD",
            );
            let newDate = "";
            const date1 = new Date();
            const date2 = new Date(maxDate);

            if (date1 < date2) {
                newDate = moment(date1).format("DD-MM-YYYY");
            } else {
                newDate = moment(date2).format("DD-MM-YYYY");
            }
            setPurchaseInvoice({
                ...purchaseInvoice,
                voucher_date: newDate,
                voucher_number: invoice?.purchaseInvoice?.invoice_number,
            });
            setChangeTax(invoice?.purchaseInvoice?.with_tax ? 1 : 0);
        }
    }, [invoice, company?.company?.currentFinancialYear]);

    useEffect(() => {
        if(invoice?.purchaseInvoice?.invoice_type){
            dispatch(rearrangeItemList(TRANSACTION_TYPE.PURCHASE, invoice?.purchaseInvoice?.invoice_type == 2 ? 1 : 2));
            setItemType(invoice?.purchaseInvoice?.invoice_type == 2 ? "item" : "accounting");
        }
    }, [invoice])

    useEffect(() => {
        if (configurationList?.header?.is_change_gst_details) {
            const gstData = calculateClassification(
                classification,
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions),
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue: gstData.totalCgstTax !== NaN ? gstData?.totalCgstTax : 0.0,
                    sgstValue: gstData?.totalSgstTax || 0,
                    igstValue: gstData?.totalIgstTax || 0,
                });
            }
        } else {
            const matchState =
                ledger?.partyDetail?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id;
            const gstData = gstCalculate(
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions),
                grandTotal,
                matchState,
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue:
                        gstData.totalCgstTax !== NaN ? parseFloat(gstData.totalCgstTax) : 0.0,
                    sgstValue: parseFloat(gstData.totalSgstTax) || 0,
                    igstValue: parseFloat(gstData.totalIgstTax) || 0,
                });
            }
        }
    }, [
        classification,
        grandTotal,
        company,
        ledger?.partyDetail,
        accountingItems,
        items,
        additionalCharges,
        addLessChanges,
        tcsRate.tcs_amount,
        configurationList?.footer?.round_off_method,
    ]);

    useEffect(() => {
        if (ledger?.partyDetail?.billingAddress?.state_id) {
            const GstType =
                company?.dispatch_address?.state_id == ledger?.partyDetail?.billingAddress?.state_id
                    ? true
                    : false;
            setIsShowGstValue(GstType);
        }
    }, [company, ledger?.partyDetail]);

    useEffect(() => {
        const payment_detail = paymentLedgerDetail?.payment_detail?.map(payment => {
            const paymentDate = purchaseInvoice.voucher_date;
            return {
                ...payment,
                pd_date: payment.is_show_invoice_date ? paymentDate : payment.pd_date,
            };
        });
        setPaymentLedgerDetail({
            ...paymentLedgerDetail,
            payment_detail: payment_detail,
        });
    }, [invoiceDetail]);

    // Modal focus tracking for ledger modal
    const modalFocusTracking = useModalFocusTracking();
    useTransactionShortcuts(formRef, modalFocusTracking);

    const handleSubmit = e => {
        const submitType = e.nativeEvent.submitter.value;
        const submit_button = submitType === "save" ? 1 : submitType == "saveAndNew" ? 2 : 3;
        const submit_button_type = submitType == "saveAndNew" ? "saveAndNew" : "";

        e.preventDefault();
        const dispatch_address = localDispatchAddress[selectedAddress] || company?.company?.billing_address;

        const isFirstDetailInvalid = detail => {
            return (
                (detail.ac_ledger_id === null && !detail.ac_value) ||
                detail.ac_value === "" ||
                (detail.ac_value == NaN && detail.ac_gst_rate_id === null) ||
                (detail.ac_gst_rate_id?.value === null && detail.ac_total === 0)
            );
        };
        const {
            itemList: item,
            is_na,
            hasNegativeTotal,
        } = prepareItemsData(
            items,
            gstCalculation,
            setGstCalculation,
            company,
            gstOptions,
            configurationList,
            changeTax,
            isInWord
        );
        const {
            ledgerList: ledger,
            is_na: is_na_ledger,
            hasNegativeLedgerTotal,
        } = prepareLedgerData(
            accountingItems,
            gstCalculation,
            setGstCalculation,
            gstOptions,
            configurationList,
            changeTax
        );
        const {isExempt, isNa} = areAllProductsExemptOrNA(itemType === "accounting" ? accountingItems : items)
        const {isAdditionalChargesExempt,isAdditionalChargesNa } = checkAllAdditionalChargesNaAndExempt(additionalCharges)

        const { is_cgst_sgst_igst_calculated, is_gst_na } = calculateGSTFlags(
            company,
            isNa,
            isExempt,
            isAdditionalChargesNa,
            isAdditionalChargesExempt
        );
        if (submitType) {
            setIsDisable(true);
            if (
                company?.company?.is_gst_applicable &&
                configurationList?.header?.is_change_gst_details &&
                !classification.classification_nature_name
            ) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Classification nature type is required.",
                        type: toastType.ERROR,
                    }),
                );
            }
            if (hasNegativeTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    }),
                );
            }

            if (hasNegativeLedgerTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    }),
                );
            }
            const submitData = {
                sale_number: invoiceDetail.invoice_number,
                voucher_number: purchaseInvoice.voucher_number,
                voucher_date: (purchaseInvoice.voucher_date),
                date_of_invoice: (invoiceDetail.invoice_date),
                supplier_ledger_id: gstQuote.party_ledger_id,
                ...(company?.company?.is_gst_applicable && {
                    gstin: gstQuote.gstin,
                }),
                billing_address: {
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                    pin_code: partyAddress.billingAddress.pin_code,
                },
                same_as_billing: sameAsBill ? 1 : 0,
                shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                shipping_name: partyAddress?.shippingAddress?.shipping_name,
                address_name: partyAddress?.shippingAddress?.address_name,
                party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0,
                ...(partyAddress?.shippingAddress?.shipping_address_id ?
                    {shipping_address_id: partyAddress?.shippingAddress?.shipping_address_id} :
                    {shipping_address: configurationList?.header?.is_enabled_shipping_address ? {
                    address_name: partyAddress?.shippingAddress?.address_name,
                    address_1: partyAddress?.shippingAddress?.address_1,
                    address_2: partyAddress?.shippingAddress?.address_2,
                    country_id: partyAddress?.shippingAddress?.country_id,
                    state_id: partyAddress?.shippingAddress?.state_id,
                    city_id: partyAddress?.shippingAddress?.city_id,
                    pin_code: partyAddress?.shippingAddress?.pin_code,
                    shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                    shipping_name: partyAddress?.shippingAddress?.shipping_name,
                    party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0
                } : null}),
                broker_details: configurationList?.header?.is_enabled_broker_details && brokerDetail?.broker_id
                    ? {
                        broker_id: brokerDetail.broker_id,
                        brokerage_for_sale: brokerDetail.broker_percentage,
                        brokerage_on_value_type: brokerDetail.brokerage_on_value,
                    }
                    : null,
                transport_details: configurationList?.header?.is_enabled_transport_details ? {
                    transport_id: transporterDetail.transport_id,
                    transporter_document_number: transporterDetail.transporter_document_number,
                    transporter_document_date: transporterDetail.transporter_document_date,
                    transporter_vehicle_number: transporterDetail.transporter_vehicle_number,
                } : null,
                eway_bill_details: configurationList?.header?.is_enabled_eway_details ? {
                    eway_bill_number: ewayBillDetail.eway_bill_number,
                    eway_bill_date: ewayBillDetail.eway_bill_date,
                } : null,
                other_details: {
                    po_no: otherDetail.po_number,
                    po_date: otherDetail.date,
                    credit_period: otherDetail.creditPeriod,
                    credit_period_type: otherDetail.creditPeriod
                        ? otherDetail.creditPeriodType
                        : null,
                },
                purchase_item_type: itemType === "item" ? 2 : 1,
                ...(itemType === "item" && { items: item }),
                ...(itemType === "accounting" && { ledgers: ledger }),
                main_classification_nature_type:
                    correctClassificationNatureType(isCheckGstType,
                        classification.classification_nature_name,
                        configurationList?.header?.is_change_gst_details,
                        partyAddress.billingAddress.state_id,
                        dispatch_address?.state_id,
                        isNa,
                        isExempt,
                        isAdditionalChargesNa,
                        isAdditionalChargesExempt,
                        true),
                is_rcm_applicable: classification.rcm_applicable ? 1 : 0,
                narration: additionalCharges?.note || null,
                term_and_condition: additionalCharges?.terms_and_conditions || null,
                ...(additionalCharges?.additional_detail?.length === 1 &&
                    isFirstDetailInvalid(additionalCharges?.additional_detail[0])
                    ? { additional_charges: " " }
                    : {
                        additional_charges: additionalCharges?.additional_detail?.map(detail => ({
                            ...detail,
                            ...(company?.company?.is_gst_applicable && {
                                ac_gst_rate_id: detail.ac_gst_rate_id?.value || null,
                            }),
                            // add_less_total: detail?.ac_total,
                            ac_total_without_tax:
                                detail?.ac_type == 2 ? detail?.ac_total : detail.ac_value,
                        })),
                    }),
                taxable_value: customToFixed(taxableValue, 2),
                total: grandTotal,
                gross_value: customToFixed(grandTotal, 2),
                ...(company?.company?.is_gst_applicable && {
                    cgst:
                        customToFixed(
                            !isIGSTCalculation && isSGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.cgstValue)
                                    : parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2,
                        ) || 0
                }),
                ...(company?.company?.is_gst_applicable && {
                    sgst:
                        customToFixed(
                            !isIGSTCalculation && isSGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.sgstValue)
                                    : parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2,
                        ) || 0
                }),
                ...(company?.company?.is_gst_applicable && {
                    igst: customToFixed(
                        isIGSTCalculation
                            ? !isEditCalculation
                                ? parseFloat(gstValue?.igstValue)
                                : parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0)
                            : 0,
                        2,
                    )
                }),
                ...(configurationList?.footer?.is_enabled_tcs_details ? {tcs_details: tcsRate.tcs_tax_id ? tcsRate : []} : {}),
                add_less: addLessChanges[0]?.al_ledger_id ? addLessChanges : [],
                grand_total: customToFixed(finalAmount, 2),
                ...(configurationList?.footer?.is_enabled_tds_details ? {tds_details: {
                    tds_tax_id: paymentLedgerDetail.tds_tax_id,
                    tds_rate: paymentLedgerDetail.tds_rate,
                    tds_amount: paymentLedgerDetail.tds_amount,
                }} : {}),
                ...(paymentLedgerDetail.payment_detail[0]?.pd_ledger_id
                    ? {
                        payment_details: paymentLedgerDetail.payment_detail,
                    }
                    : {
                        payment_details: " ",
                    }),
                cess: customToFixed(cessValue, 2),
                round_off_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_gst_enabled: !company?.company?.is_gst_applicable
                    ? 0
                    : gstCalculation.is_gst_enabled || 1,
                rounding_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_cgst_sgst_igst_calculated,
                is_gst_na,
                is_round_off_not_changed: gstCalculation.is_round_off_not_changed ?? 1,
                submit_button_value: submit_button,
                advance_payment: selectedAdvancePayment,
                // custom_fields: customFieldListTransaction?.flatMap((customField) =>
                //     customField.value ? [{ ...(customField?.id ? {id: customField?.id} : {}), custom_field_id: customField.custom_field_id, value: customField.value }] : []
                // ),
                custom_fields: customFieldListTransaction?.flatMap(customField =>
                    customField.value &&
                    (customField.is_enabled === undefined || customField.is_enabled)
                        ? [
                              {
                                  ...(customField?.id ? { id: customField.id } : {}),
                                  custom_field_id: customField.custom_field_id,
                                  value: customField.value,
                              },
                          ]
                        : []
                ),
                round_off_method: (purchaseOrderId || id || isDuplicate) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
            };
            const formData = convertToFormData(submitData);

            {
                gstQuote?.purchase_number &&
                    formData.append(
                        "purchase_order_no",
                        gstQuote?.purchase_number &&
                        gstQuote?.purchase_number.map(quote => quote?.value),
                    );
            }
            if (additionalCharges?.upload_document) {
                additionalCharges.upload_document.forEach((doc, index) => {
                    if (doc.file) {
                        formData.append(`upload_purchase_invoice[${index}]`, doc.file ? doc.file : " ");
                    }
                });
            }
            if (id) {
                dispatch(updatePurchase(id, formData, submitType, submit_button, setIsDisable));
            } else {
                dispatch(
                    addPurchase(
                        formData,
                        submitType === "saveAndNew" ? "duplicate" : submitType,
                        submit_button,
                        setIsDisable,
                        submitData,
                        submit_button_type
                    ),
                );
            };
            setisFieldsChanges(false);
            setHasUnsavedChanges(false);
        }
    };

    useEffect(() => {
        setBrokerDetail({
            ...brokerDetail,
            broker_percentage: broker?.getBrokerDetailsById?.brokerage_for_purchase || "",
            brokerage_on_value: broker?.getBrokerDetailsById?.brokerage_for_purchase_type || "",
        });
    }, [broker?.getBrokerDetailsById]);

    const salesUrl = "/company/purchases";
    const purchasePrevUrl = `${url}${salesUrl}/${prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
        }`;
    const purchaseNextUrl = `${url}${salesUrl}/${prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
        }`;

    const handleDeleteTransaction = () => {
        if (id) {
            openDeleteTransactionModel()
        }
    };

    const handleConfirmDelete = () => {
        if (id) {
            dispatch(deletePurchase(id, setShowDeleteWarningModel));
            closeDeleteTransactionModel();
        }
    };

    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBack = () => {
        if(isFieldsChanges) {
            setHasUnsavedChanges(true);
        };
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);

    return (
        <>
            <CustomHelmet
                title={id ? "Edit Purchase" : "Add Purchase"}
            />
            {loader ? (
                <Loader />
            ) : (purchase.status === 404 || purchaseOrder.status === 404) && !window.location.pathname.includes("/create")  ? (
                <Error404Page />
            ) : (
                <div className="ms-3 mt-12" defaultActiveKey={"edit"} id="uncontrolled-tab-example">
                    <form ref={formRef} onSubmit={handleSubmit} onInput={handleInput} >
                        <Container fluid className="p-0">
                            <div className="content-wrapper-invoice py-6 px-lg-10 px-sm-8 px-6">
                                {
                                    <SaleInvoiceDetail
                                        tableHeaderList={tableHeaderList}
                                        accountingTableHeader={accountingTableHeader}
                                        isShowInvoiceDetails
                                        isShowEstimateOrChallan={false}
                                        isShowPurchaseNumber
                                        isShowPurchaseInvoiceDetails
                                        isPurchase={true}
                                        id={id}
                                        purchaseReturnRef={purchaseReturnRef}
                                        ledgerModalName={"Add Supplier"}
                                        ledgerModalEditName={"Update Supplier"}
                                        isDuplicate={isDuplicate}
                                        purchaseOrderId
                                        shipping_address_type={shippingAddressType}
                                        isInWord={true}
                                    />
                                }
                            </div>
                            <div className="custom-nav-tabs nav-tabs d-flex">
                                <a
                                    href={ !isFieldsChanges ? purchasePrevUrl : undefined}
                                    onClick={(e) => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(purchasePrevUrl);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn prev-btn d-flex justify-content-center ${!prevNext?.fetchPrevNextUrl?.previousBillId && "disabled"
                                        } align-items-center cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-left"></i>
                                </a>
                                <a
                                    href={ !isFieldsChanges ? purchaseNextUrl : undefined}
                                    onClick={(e) => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(purchaseNextUrl);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn next-btn d-flex justify-content-center align-items-center  ${!prevNext?.fetchPrevNextUrl?.nextBillId && !id && "disabled"
                                        } me-3 cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-right"></i>
                                </a>
                                <div className="nav-item">
                                    <div
                                        className="nav-link cursor-pointer"
                                        onClick={() =>
                                            window.open(
                                                "https://www.youtube.com/watch?v=-QZ8JshWjWg",
                                            )
                                        }
                                    >
                                        <Youtube />
                                    </div>
                                </div>
                                <ConfigurationModal roundOffOption={roundOffOption} />
                            </div>
                        </Container>
                        <SaleItems
                            isPurchase
                            items={itemType === "item" ? items : accountingItems}
                            setItems={itemType === "item" ? setItems : setAccountingItems}
                            grandTotal={grandTotal}
                            setGrandTotal={setGrandTotal}
                            mainGrandTotal={mainGrandTotal}
                            shippingValue={shippingValue}
                            setShippingValue={setShippingValue}
                            tcsValue={tcsValue}
                            setTCSValue={setTCSValue}
                            tcsRateValue={tcsRateValue}
                            setTCSRateValue={setTCSRateValue}
                            packingCharge={packingCharge}
                            setPackingCharge={setPackingCharge}
                            tableHeader={updatedTableHeader}
                            tableHeaderList={tableHeaderList}
                            accountingTableHeader={accountingTableHeader}
                            isShowGstValue={isShowGstValue}
                            taxableValue={taxableValue}
                            setFinalAmount={setFinalAmount}
                            finalAmount={finalAmount}
                            isShowPaymentDetails={true}
                            settlePaymentType={"purchase"}
                            shipping_address_type={shippingAddressType}
                            isInWord={isInWord}
                        />
                        <Container fluid className="p-0 mt-10 fixed-bottom-section">
                            <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="save"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save
                                </button>
                                {(!id || isDuplicate) && (
                                    <button
                                        type="submit"
                                        name="submitType"
                                        value="saveAndNew"
                                        className="btn btn-primary"
                                        disabled={isDisable}
                                    >
                                        Save & New
                                    </button>
                                )}
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="saveAndPrint"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save & Print
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        if(isFieldsChanges) {
                                            setUnsavedBackUrl(`${window.location.origin}${salesUrl}`);
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        } else {
                                            (window.location.href = `${window.location.origin}${salesUrl}`)
                                        };
                                    }
                                    }
                                >
                                    Back
                                </button>
                                {id && (
                                    <button
                                        onClick={handleDeleteTransaction}
                                        className="btn btn-danger"
                                    >
                                        Delete
                                    </button>
                                )}
                            </div>
                        </Container>
                    </form>
                </div>
            )}
            <Toast />
            {deleteTransaction && <WarningModal
                show={deleteTransaction}
                title="Delete!"
                message='Are you sure want to delete this transaction?'
                showCancelButton
                showConfirmButton
                confirmText="Yes, Delete"
                cancelText="No, Cancel"
                handleClose={closeDeleteTransactionModel}
                handleSubmit={() => handleConfirmDelete()}
            />}
            {showDeleteWarningModel &&
                <DeleteWarningModal show={showDeleteWarningModel?.show} handleClose={() => setShowDeleteWarningModel({ show: false })} transactions={showDeleteWarningModel?.transactions} />}
        </>
    );
};

export default PurchasesTransaction;
