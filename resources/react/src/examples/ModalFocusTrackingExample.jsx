import React, { useState } from 'react';
import { <PERSON>dal, Button, Form } from 'react-bootstrap';
import { useModalWithFocusTracking, useTransactionShortcuts } from '../shared/shortcut-keys';

const ModalFocusTrackingExample = () => {
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({ name: '', email: '', phone: '' });
  
  // Use the modal focus tracking hook
  const { modalFocusTracking, openModalWithTracking, closeModalWithTracking } = useModalWithFocusTracking();
  
  // Use transaction shortcuts with modal focus tracking
  useTransactionShortcuts(null, modalFocusTracking);

  // Handle opening modal with focus tracking
  const handleOpenModal = () => {
    openModalWithTracking(() => setShowModal(true));
  };

  // Handle closing modal with focus tracking
  const handleCloseModal = () => {
    closeModalWithTracking(() => setShowModal(false));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Close modal and focus on next field
    handleCloseModal();
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="container mt-4">
      <h2>Modal Focus Tracking Example</h2>
      <p>This example demonstrates how to track the next field when a modal opens and focus on it when the modal is saved.</p>
      
      {/* Main form with focusable elements */}
      <Form className="mb-4">
        <div className="row">
          <div className="col-md-4 mb-3">
            <Form.Group>
              <Form.Label>First Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter first name"
                name="firstName"
              />
            </Form.Group>
          </div>
          <div className="col-md-4 mb-3">
            <Form.Group>
              <Form.Label>Last Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter last name"
                name="lastName"
              />
            </Form.Group>
          </div>
          <div className="col-md-4 mb-3">
            <Form.Group>
              <Form.Label>Age</Form.Label>
              <Form.Control
                type="number"
                placeholder="Enter age"
                name="age"
              />
            </Form.Group>
          </div>
        </div>
        
        <div className="row">
          <div className="col-md-6 mb-3">
            <Form.Group>
              <Form.Label>Address</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                placeholder="Enter address"
                name="address"
              />
            </Form.Group>
          </div>
          <div className="col-md-6 mb-3">
            <Form.Group>
              <Form.Label>City</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter city"
                name="city"
              />
            </Form.Group>
          </div>
        </div>

        <Button variant="primary" onClick={handleOpenModal}>
          Open Modal (Focus will be tracked)
        </Button>
      </Form>

      {/* Modal with focus tracking */}
      <Modal show={showModal} onHide={handleCloseModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>Contact Information</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                autoFocus
              />
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Email</Form.Label>
              <Form.Control
                type="email"
                placeholder="Enter email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
              />
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Phone</Form.Label>
              <Form.Control
                type="tel"
                placeholder="Enter phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </Form.Group>
            
            <div className="d-flex justify-content-end">
              <Button variant="secondary" onClick={handleCloseModal} className="me-2">
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                Save
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-light rounded">
        <h5>How to test:</h5>
        <ol>
          <li>Focus on any input field in the main form</li>
          <li>Click "Open Modal" button or use Ctrl+A to open modal</li>
          <li>Fill out the modal form</li>
          <li>Click "Save" or use Ctrl+A to submit the modal</li>
          <li>Notice that focus automatically moves to the next field after the one you were on before opening the modal</li>
        </ol>
        <p><strong>Keyboard shortcuts:</strong></p>
        <ul>
          <li><kbd>Ctrl+A</kbd> - Submit modal (if modal is open) or save transaction</li>
          <li><kbd>Enter</kbd> - Move to next focusable element</li>
        </ul>
      </div>
    </div>
  );
};

export default ModalFocusTrackingExample;
