import { useEffect, useContext, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { StateContext } from "../context/StateContext";
import { ROUTES } from "../constants";

export const useTransactionShortcuts = (formRef, modalFocusTracking = null) => {
    useEffect(() => {
        const handleKeyDown = e => {
            // Handle Enter key for navigation
            if (e.key === "Enter" && !e.ctrlKey && !e.shiftKey && !e.altKey) {
                // Get all focusable elements - form inputs and useful buttons
                const focusableElements = document.querySelectorAll(
                    'input:not([disabled]):not([type="hidden"]):not([type="submit"]):not([type="button"]):not([tabindex="-1"]):not([aria-hidden="true"]), textarea:not([disabled]), select:not([disabled]), button:not([disabled])'
                );

                // Filter out elements that are not visible or not focusable
                const focusableArray = Array.from(focusableElements).filter(element => {
                    const style = window.getComputedStyle(element);
                    const rect = element.getBoundingClientRect();
                    const tagName = element.tagName.toLowerCase();

                    // Include form inputs and useful buttons
                    const isValidFormElement =
                        tagName === "input" ||
                        tagName === "textarea" ||
                        tagName === "select" ||
                        tagName === "button";

                    return (
                        isValidFormElement &&
                        style.display !== "none" &&
                        style.visibility !== "hidden" &&
                        element.offsetParent !== null &&
                        !element.hasAttribute("readonly") &&
                        !element.hasAttribute("aria-hidden") &&
                        element.getAttribute("tabindex") !== "-1" &&
                        rect.width > 0 &&
                        rect.height > 0 &&
                        !element.className.includes("requiredInput") &&
                        !element.className.includes("RequiredInput") &&
                        !element.className.includes("barcode-print-focus-button") &&
                        !element.className.includes("edit-icon") &&
                        !element.className.includes("nav-link") &&
                        // Exclude anything with unit_price in ID
                        !element.id.includes("unit_price") &&
                        !element.closest('.input-group-text.custom-group-text') &&
                        element.name !== "sales_item_type" &&
                        !element.className.includes("country-list") &&
                        (!element.hasAttribute("role") ||
                         (element.hasAttribute("role") && !["listbox", "menu", "menubar"].includes(element.getAttribute("role"))))
                    );
                });

                const currentElement = e.target;
                const currentIndex = focusableArray.indexOf(currentElement);

                if (currentIndex !== -1 && currentIndex < focusableArray.length - 1) {
                    e.preventDefault();
                    // Move to next focusable element
                    const nextElement = focusableArray[currentIndex + 1];
                    nextElement.focus();
                }
                return;
            }

            if (e.ctrlKey) {
                switch (e.keyCode) {
                    case 65:
                        e.preventDefault();

                        // Check if any modals are currently open (get all open modals)
                        const openModals = document.querySelectorAll(
                            ".modal.show, .modal.fade.show"
                        );
                        const modalBackdrop = document.querySelector(".modal-backdrop");

                        if (openModals.length > 0 || modalBackdrop) {
                            // Find the most recently opened modal (last in DOM order)
                            // This represents the modal that was opened most recently and should be closed first
                            const topmostModal = openModals[openModals.length - 1];

                            if (topmostModal) {
                                // Try to submit the most recently opened modal
                                let modalSubmitButton =
                                    topmostModal.querySelector('button[type="submit"]');

                                // If no submit button found, look for buttons with "Save" text
                                if (!modalSubmitButton) {
                                    const buttons = topmostModal.querySelectorAll("button");
                                    if (buttons) {
                                        for (const button of buttons) {
                                            if (
                                                button.textContent?.trim().toLowerCase() === "save"
                                            ) {
                                                modalSubmitButton = button;
                                                break;
                                            }
                                        }
                                    }
                                }

                                // If still no button found, look for primary buttons (common pattern)
                                if (!modalSubmitButton) {
                                    modalSubmitButton = topmostModal.querySelector(".btn-primary");
                                }

                                if (modalSubmitButton) {
                                    // If modal focus tracking is available, focus on tracked field after modal closes
                                    if (modalFocusTracking?.focusOnTrackedField) {
                                        // Add event listener to focus on tracked field after modal closes
                                        const handleModalClose = () => {
                                            modalFocusTracking.focusOnTrackedField();
                                            // Remove the event listener after use
                                            topmostModal.removeEventListener('hidden.bs.modal', handleModalClose);
                                        };
                                        topmostModal.addEventListener('hidden.bs.modal', handleModalClose);
                                    }
                                    // Click the submit button
                                    modalSubmitButton.click();
                                } else {
                                    // Try to find and submit the form directly
                                    const modalForm = topmostModal.querySelector("form");
                                    if (modalForm) {
                                        // If modal focus tracking is available, focus on tracked field after modal closes
                                        if (modalFocusTracking?.focusOnTrackedField) {
                                            const handleModalClose = () => {
                                                modalFocusTracking.focusOnTrackedField();
                                                topmostModal.removeEventListener('hidden.bs.modal', handleModalClose);
                                            };
                                            topmostModal.addEventListener('hidden.bs.modal', handleModalClose);
                                        }
                                        // Trigger form submission
                                        const submitEvent = new Event("submit", {
                                            bubbles: true,
                                            cancelable: true,
                                        });
                                        modalForm.dispatchEvent(submitEvent);
                                    }
                                }
                            }
                        } else {
                            // No modal is open - proceed with normal save transaction behavior
                            formRef?.current
                                ?.querySelector('[name="submitType"][value="save"]')
                                ?.click();
                        }
                        break;
                    case 78:
                        e.preventDefault();
                        formRef?.current
                            ?.querySelector('[name="submitType"][value="saveAndNew"]')
                            ?.click();
                        break;
                    case 80:
                        e.preventDefault();
                        formRef?.current
                            ?.querySelector('[name="submitType"][value="saveAndPrint"]')
                            ?.click();
                        break;
                    case 113:
                        if (typeof formRef === "string" && formRef.trim() !== "") {
                            $(`#${formRef}`).trigger("click");
                        }
                        break;
                    default:
                        break;
                }
            }
        };

        document.addEventListener("keydown", handleKeyDown);
        return () => document.removeEventListener("keydown", handleKeyDown);
    }, [formRef]);
};

export const useKeyboardShortcuts = () => {
  const navigate = useNavigate();
  const stateContext = useContext(StateContext);

  // Safely extract values from context with fallbacks
  const hasUnsavedChanges = stateContext?.hasUnsavedChanges || false;
  const isFieldsChanges = stateContext?.isFieldsChanges || false;

  // Keep track of the last focused element for refocusing
  const lastFocusedElementRef = useRef(null);

  useEffect(() => {
    // Clear the last focused element reference when the component mounts or page changes
    lastFocusedElementRef.current = null;
  }, []);

  // useEffect(() => {
  //   const handleKeyDown = (e) => {
  //     // Skip if any modifier keys are pressed except for our specific combinations
  //     // if (e.altKey && e.key !== 'F8') return;
  //     // Allow Windows+A (metaKey + 'a') and Ctrl+A combinations
  //     if (e.metaKey && !(e.key === 'a')) return;

  //     // // F8 Key Press Behavior
  //     // if (e.key === 'F8') {
  //     //   e.preventDefault();

  //     //   // If there is an existing record being edited, do not overwrite it
  //     //   // Instead, redirect to Dashboard page
  //     //   // Note: We navigate regardless of unsaved changes as per requirement
  //     //   // The requirement states "do not overwrite" but still redirect to dashboard
  //     //   try {
  //     //     navigate(ROUTES.DASHBOARD);
  //     //   } catch (error) {
  //     //     console.warn('Navigation to dashboard failed:', error);
  //     //     // Fallback to window location
  //     //     window.location.href = '/company/dashboard';
  //     //   }
  //     //   return;
  //     // }

  //     // Escape Key Behavior
  //     if (e.key === 'Escape') {
  //       // Check if there's an open modal first
  //       const openModal = document.querySelector('.modal.show, .modal.fade.show, [role="dialog"][aria-hidden="false"]');
  //       const openBootstrapModal = document.querySelector('.modal-backdrop');
  //       const openSweetAlert = document.querySelector('.swal2-container');
  //       const openDropdown = document.querySelector('.dropdown-menu.show');

  //       // If there's an open modal, tooltip, dropdown, or SweetAlert, let the default behavior handle it
  //       if (openModal || openBootstrapModal || openSweetAlert || openDropdown) {
  //         // Don't prevent default - let the modal/component handle the escape key
  //         return;
  //       }

  //       e.preventDefault();

  //       // Check if an input field is currently focused
  //       const activeElement = document.activeElement;

  //       const isInputFocused = activeElement && (
  //         activeElement.tagName === 'INPUT' ||
  //         activeElement.tagName === 'TEXTAREA' ||
  //         activeElement.tagName === 'SELECT' ||
  //         activeElement.isContentEditable ||
  //         activeElement.getAttribute('contenteditable') === 'true'
  //       );

  //       if (isInputFocused) {
  //         // Store the currently focused element and remove focus
  //         lastFocusedElementRef.current = activeElement;
  //         activeElement.blur();
  //       } else {
  //         // No input is focused - check if we have a previously focused element to refocus
  //         const storedElement = lastFocusedElementRef.current;
  //         const isStoredElementValid = storedElement &&
  //           document.contains(storedElement) &&
  //           storedElement.offsetParent !== null && // Element is visible
  //           !storedElement.disabled; // Element is not disabled

  //         if (isStoredElementValid) {
  //           // Check if the stored element is on the current page by checking if it's in the current viewport context
  //           const currentPageInputs = document.querySelectorAll('input, textarea, select, [contenteditable="true"]');
  //           const isElementOnCurrentPage = Array.from(currentPageInputs).includes(storedElement);

  //           if (isElementOnCurrentPage) {
  //             // Refocus the last focused element
  //             try {
  //               storedElement.focus();
  //               // Clear the reference after refocusing
  //               lastFocusedElementRef.current = null;
  //             } catch (error) {
  //               console.warn('Failed to refocus element:', error);
  //               lastFocusedElementRef.current = null;
  //             }
  //           } else {
  //             // Element is not on current page, clear reference and navigate back
  //             lastFocusedElementRef.current = null;
  //             try {
  //               window.history.back();
  //             } catch (error) {
  //               console.warn('Browser back navigation failed:', error);
  //             }
  //           }
  //         } else {
  //           console.log('No valid element to refocus');
  //           // No valid element to refocus, clear reference and navigate back
  //           lastFocusedElementRef.current = null;
  //           try {
  //             window.history.back();
  //           } catch (error) {
  //             console.warn('Browser back navigation failed:', error);
  //           }
  //         }
  //       }
  //       return;
  //     }

  //     // Ctrl + A, Cmd + A (Mac), and Windows + A behavior
  //     if ((e.ctrlKey && e.key === 'a') || (e.metaKey && e.key === 'a')) {
  //       // Check if user is typing inside a text input or textarea
  //       const activeElement = document.activeElement;
  //       const isTextInput = activeElement && (
  //         (activeElement.tagName === 'INPUT' &&
  //          ['text', 'email', 'password', 'search', 'url', 'tel', 'number'].includes(activeElement.type)) ||
  //         activeElement.tagName === 'TEXTAREA' ||
  //         activeElement.isContentEditable ||
  //         activeElement.getAttribute('contenteditable') === 'true'
  //       );

  //       // If user is typing in a text field, allow default select-all behavior
  //       if (isTextInput) {
  //         return;
  //       }

  //       // Otherwise, prevent default and open Sale screen
  //       e.preventDefault();
  //       try {
  //         navigate(`${ROUTES.SALES}/create`);
  //       } catch (error) {
  //         console.warn('Navigation to sales create failed:', error);
  //         // Fallback to window location
  //         window.location.href = '/company/sales/create';
  //       }
  //       return;
  //     }
  //   };

  //   document.addEventListener("keydown", handleKeyDown);
  //   return () => document.removeEventListener("keydown", handleKeyDown);
  // }, [navigate, hasUnsavedChanges, isFieldsChanges]);
};

// Modal focus tracking functionality
export const useModalFocusTracking = () => {
  const nextFieldToFocusRef = useRef(null);

  // Function to get all focusable elements (same logic as Enter key navigation)
  const getFocusableElements = () => {
    const focusableElements = document.querySelectorAll(
      'input:not([disabled]):not([type="hidden"]):not([type="submit"]):not([type="button"]):not([tabindex="-1"]):not([aria-hidden="true"]), textarea:not([disabled]), select:not([disabled]), button:not([disabled])'
    );

    return Array.from(focusableElements).filter(element => {
      const style = window.getComputedStyle(element);
      const rect = element.getBoundingClientRect();
      const tagName = element.tagName.toLowerCase();

      const isValidFormElement =
        tagName === "input" ||
        tagName === "textarea" ||
        tagName === "select" ||
        tagName === "button";

      return (
        isValidFormElement &&
        style.display !== "none" &&
        style.visibility !== "hidden" &&
        element.offsetParent !== null &&
        !element.hasAttribute("readonly") &&
        !element.hasAttribute("aria-hidden") &&
        element.getAttribute("tabindex") !== "-1" &&
        rect.width > 0 &&
        rect.height > 0 &&
        !element.className.includes("requiredInput") &&
        !element.className.includes("RequiredInput") &&
        !element.className.includes("barcode-print-focus-button") &&
        !element.className.includes("edit-icon") &&
        !element.className.includes("nav-link") &&
        !element.id.includes("unit_price") &&
        !element.closest('.input-group-text.custom-group-text') &&
        element.name !== "sales_item_type" &&
        !element.className.includes("country-list") &&
        (!element.hasAttribute("role") ||
         (element.hasAttribute("role") && !["listbox", "menu", "menubar"].includes(element.getAttribute("role"))))
      );
    });
  };

  // Function to track next field when modal opens
  const trackNextFieldOnModalOpen = () => {
    const currentElement = document.activeElement;
    if (currentElement) {
      const focusableElements = getFocusableElements();
      const currentIndex = focusableElements.indexOf(currentElement);

      if (currentIndex !== -1 && currentIndex < focusableElements.length - 1) {
        // Store the next focusable element
        nextFieldToFocusRef.current = focusableElements[currentIndex + 1];
      } else {
        // If current element is the last one or not found, store the first focusable element
        nextFieldToFocusRef.current = focusableElements[0] || null;
      }
    } else {
      // If no element is focused, store the first focusable element
      const focusableElements = getFocusableElements();
      nextFieldToFocusRef.current = focusableElements[0] || null;
    }
  };

  // Function to focus on tracked field when modal closes
  const focusOnTrackedField = () => {
    if (nextFieldToFocusRef.current && document.contains(nextFieldToFocusRef.current)) {
      // Check if the element is still visible and focusable
      const element = nextFieldToFocusRef.current;
      const style = window.getComputedStyle(element);
      const rect = element.getBoundingClientRect();

      if (
        style.display !== "none" &&
        style.visibility !== "hidden" &&
        element.offsetParent !== null &&
        !element.disabled &&
        rect.width > 0 &&
        rect.height > 0
      ) {
        setTimeout(() => {
          element.focus();
          nextFieldToFocusRef.current = null; // Clear the reference after focusing
        }, 100); // Small delay to ensure modal is fully closed
      } else {
        nextFieldToFocusRef.current = null; // Clear invalid reference
      }
    }
  };

  return {
    trackNextFieldOnModalOpen,
    focusOnTrackedField,
    nextFieldToFocusRef
  };
};

// Helper hook for components to easily integrate modal focus tracking
export const useModalWithFocusTracking = () => {
  const modalFocusTracking = useModalFocusTracking();

  // Function to call when opening a modal
  const openModalWithTracking = (openModalFunction) => {
    modalFocusTracking.trackNextFieldOnModalOpen();
    if (typeof openModalFunction === 'function') {
      openModalFunction();
    }
  };

  // Function to call when closing a modal (for manual close handlers)
  const closeModalWithTracking = (closeModalFunction) => {
    if (typeof closeModalFunction === 'function') {
      closeModalFunction();
    }
    // Focus on tracked field after a small delay to ensure modal is closed
    setTimeout(() => {
      modalFocusTracking.focusOnTrackedField();
    }, 100);
  };

  return {
    modalFocusTracking,
    openModalWithTracking,
    closeModalWithTracking
  };
};

export const handleShortcutKeys = handleKeyPress => {
    useEffect(() => {
        // attach the event listener
        window.addEventListener("keydown", handleKeyPress);
        // remove the event listener
        return () => {
            window.removeEventListener("keydown", handleKeyPress);
        };
    }, [handleKeyPress]);
};
